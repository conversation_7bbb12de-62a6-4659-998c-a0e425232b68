import logging
import tempfile
import os
from typing import Dict, Any, Optional, List
from lxml import html
import re
from datetime import datetime

logger = logging.getLogger(__name__)


class HTMLExtractionService:
    """Service for extracting data from HTML complaint files with complete website logic"""

    def __init__(self):
        self.logger = logger
    
    async def extract_html_data(
        self,
        html_content: str,
        max_layer: int = 7,
        fraud_type: Optional[str] = None,
        extraction_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Main extraction method that processes HTML content and returns structured data
        
        Args:
            html_content: Raw HTML content from uploaded file
            max_layer: Maximum layer to extract (0-7)
            fraud_type: Type of fraud (online_banking, card, etc.)
            extraction_type: Specialized extraction logic (banking_upi, card, etc.)
            
        Returns:
            Dictionary containing extracted metadata, transactions, and other data
        """
        try:
            # Sanitize HTML content for safe processing
            sanitized_html = self._sanitize_html(html_content)
            
            # Parse HTML
            tree = html.fromstring(sanitized_html)
            
            # Log structure for debugging
            self.logger.info(f"HTML parsed successfully. Elements found: {len(tree.xpath('//*'))}")
            
            # Extract metadata
            metadata = self._extract_metadata(tree)
            self.logger.info(f"Metadata extracted: {metadata}")
            
            # Extract transactions based on type
            if extraction_type:
                self.logger.info(f"Using specialized extraction: {extraction_type}")
                all_transactions = self._extract_with_custom_logic(
                    tree, extraction_type, max_layer
                )
            else:
                self.logger.info(f"Using standard extraction with fraud type: {fraud_type}")
                all_transactions = self._extract_transactions(tree, fraud_type, max_layer)
            
            # Process layer transactions
            layer_transactions = self._process_layer_transactions(all_transactions, max_layer)
            
            # Generate additional data structures
            bank_notice_data = self._generate_bank_notice_data(all_transactions, metadata)
            graph_data = self._generate_graph_data(all_transactions, metadata, max_layer)
            
            result = {
                "metadata": metadata,
                "transactions": all_transactions,
                "layer_transactions": layer_transactions,
                "bank_notice_data": bank_notice_data,
                "graph_data": graph_data,
                "extraction_info": {
                    "max_layer": max_layer,
                    "fraud_type": fraud_type,
                    "extraction_type": extraction_type,
                    "total_transactions": len(all_transactions),
                    "extracted_at": datetime.utcnow().isoformat()
                }
            }
            
            self.logger.info(f"Extraction completed. Total transactions: {len(all_transactions)}")
            return result
            
        except Exception as e:
            self.logger.error(f"HTML extraction failed: {str(e)}", exc_info=True)
            raise Exception(f"Failed to extract HTML data: {str(e)}")
    
    def _sanitize_html(self, html_content: str) -> str:
        """
        Minimal sanitization for extraction - removes dangerous scripts while preserving content
        """
        if not html_content:
            return ""
        
        # Remove script tags and their content
        html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
        
        # Remove event handlers
        html_content = re.sub(r'\s*on\w+\s*=\s*["\'][^"\']*["\']', '', html_content, flags=re.IGNORECASE)
        
        # Remove javascript: URLs
        html_content = re.sub(r'javascript:[^"\']*', '', html_content, flags=re.IGNORECASE)
        
        return html_content

    def _clean_text(self, text):
        """Clean text by removing whitespace, newlines, and other unwanted characters"""
        if not text:
            return ""
        return text.strip().replace('\n', ' ').replace('\r', '').replace('\t', ' ').replace('  ', ' ')

    def _should_skip_row(self, remarks):
        """Determine if a row should be skipped based on its remarks"""
        if not remarks:
            return False

        skip_keywords = [
            "duplicate entry", "already attended", "details already shared",
            "information already provided", "already mentioned", "wrong transaction",
            "low value transaction less than or equal to 500",
            "same transaction id / utr no. and amount. already replied in this mha.",
            "already action taken for same utr number", "invalid transaction id provided"
        ]

        remarks_lower = remarks.lower()
        for keyword in skip_keywords:
            if keyword in remarks_lower:
                return True
        return False



    def _clean_account_number(self, account_no):
        """Helper function to clean account numbers by removing common prefixes"""
        if not account_no:
            return ""

        account_str = str(account_no)
        account_lower = account_str.lower()

        prefixes = [
            "a/c no.-:", "a/c no.:", "a/c no.", "a/c",
            "A/C No.-:", "A/C No.:", "A/C No.", "A/C",
            "account no.-:", "account no.:", "account no.", "account"
        ]

        for prefix in prefixes:
            if prefix.lower() in account_lower:
                account_str = account_str.replace(prefix, "")

        if account_str.startswith("-:"):
            account_str = account_str[2:]
        if account_str.startswith("- :"):
            account_str = account_str[3:]
        if account_str.startswith("-: "):
            account_str = account_str[3:]

        return account_str.strip()


    
    def _extract_metadata(self, tree) -> Dict[str, Any]:
        """Extract metadata from HTML tree using complete website logic"""
        def get_text_by_id(id_: str) -> str:
            """Helper to extract text by element ID"""
            try:
                self.logger.info(f"Extracting element with ID: {id_}")
                element_exists = tree.xpath(f"//*[@id='{id_}']")
                self.logger.info(f"Element with ID '{id_}' exists: {bool(element_exists)}")

                elements = tree.xpath(f"//*[@id='{id_}']/text()")
                result = elements[0].strip() if elements else ""
                self.logger.info(f"Extracting {id_}: Found {len(elements)} elements, value: '{result}'")
                return result
            except Exception as e:
                self.logger.warning(f"Failed to extract element {id_}: {e}")
                return ""

        def get_text_by_label(label_text: str) -> str:
            """Helper to extract text by label"""
            try:
                self.logger.info(f"Searching for label with text: '{label_text}'")
                labels = tree.xpath(f"//label[contains(text(), '{label_text}')]")
                self.logger.info(f"Found {len(labels)} labels containing text '{label_text}'")

                elements = tree.xpath(f"//label[contains(text(), '{label_text}')]/following::span[1]/text()")
                result = elements[0].strip() if elements else ""
                self.logger.info(f"Extracted value for label '{label_text}': '{result}'")
                return result
            except Exception as e:
                self.logger.warning(f"Failed to extract by label {label_text}: {e}")
                return ""

        # Extract acknowledgement number with validation
        acknowledgement_no = get_text_by_id("txtcrimeId")
        if acknowledgement_no and not acknowledgement_no.isdigit():
            acknowledgement_no = ""

        # Try fallback for acknowledgement number
        if not acknowledgement_no:
            temp_ack = get_text_by_label("Acknowledgement No")
            if temp_ack and temp_ack.isdigit():
                acknowledgement_no = temp_ack

        # Extract category and subcategory with fallbacks
        category = get_text_by_id("ContentPlaceHolder1_txtcategoryCrime") or get_text_by_id("ContentPlaceHolder1_lblCatCrime")
        if not category:
            category = get_text_by_label("Category of Complaint")

        subcategory = get_text_by_id("ContentPlaceHolder1_txtcrimesubcat") or get_text_by_id("ContentPlaceHolder1_lblsubcat")
        if not subcategory:
            subcategory = get_text_by_label("Sub Category of Complaint")

        # Extract date with fallbacks
        date = get_text_by_id("ContentPlaceHolder1_lblincidentdatetime") or get_text_by_id("ContentPlaceHolder1_lblComplaintDate")
        if not date:
            date = get_text_by_label("Complaint Date")

        # Extract complainant details
        mobile_number = get_text_by_id("ContentPlaceHolder1_lblMNo") or get_text_by_label("Mobile No.")
        email_id = get_text_by_id("ContentPlaceHolder1_lblEmailId")
        relationship = get_text_by_id("ContentPlaceHolder1_lblRelation")
        gender = get_text_by_id("ContentPlaceHolder1_lblgender") or get_text_by_label("Gender")

        # Extract address details
        house_no = get_text_by_id("ContentPlaceHolder1_lblCHNo") or get_text_by_label("House No.")
        street = get_text_by_id("ContentPlaceHolder1_lblCStreet") or get_text_by_label("Street Name")
        colony = get_text_by_id("ContentPlaceHolder1_lblCColony") or get_text_by_label("Colony")
        village_town_city = get_text_by_id("ContentPlaceHolder1_lblCVill") or get_text_by_label("Village/ Town/ City")
        tehsil = get_text_by_id("ContentPlaceHolder1_lblCTehsil") or get_text_by_label("Tehsil")
        district = get_text_by_id("ContentPlaceHolder1_lblCDist") or get_text_by_label("District")
        state = get_text_by_id("ContentPlaceHolder1_lblCState") or get_text_by_label("State")
        country = get_text_by_id("ContentPlaceHolder1_lblCCount") or get_text_by_label("Country")
        police_station = get_text_by_id("ContentPlaceHolder1_lblCPStation") or get_text_by_label("Police Station")
        pin_code = get_text_by_id("ContentPlaceHolder1_lblCpin") or get_text_by_label("Pin Code")

        # Complete metadata structure matching website backend
        metadata = {
            "total_amount": get_text_by_id("ContentPlaceHolder1_lbltotalfraudlentamt") or get_text_by_label("Total Fraudulent Amount"),
            "lien_amount": get_text_by_id("ContentPlaceHolder1_lbltotallienAmount") or get_text_by_label("Total Lien Amount"),
            "date": date,
            "complaint_number": acknowledgement_no,
            "category": category,
            "subcategory": subcategory,
            "complainant_name": get_text_by_id("ContentPlaceHolder1_lblName") or get_text_by_label("Name"),
            "complainant_mobile": mobile_number,
            "complainant_email": email_id,
            "complainant_relationship": relationship,
            "complainant_gender": gender,
            "address": {
                "house_no": house_no,
                "street": street,
                "colony": colony,
                "village_town_city": village_town_city,
                "tehsil": tehsil,
                "district": district,
                "state": state,
                "country": country,
                "police_station": police_station,
                "pin_code": pin_code
            }
        }

        # Clean up empty values
        metadata = {k: v for k, v in metadata.items() if v}

        return metadata
    
    def _extract_transactions(self, tree, fraud_type: Optional[str], max_layer: int) -> List[Dict[str, Any]]:
        """Extract transactions using standard logic"""
        if fraud_type == "card":
            return self._extract_card_transactions(tree, max_layer)
        else:
            # Default to banking/UPI extraction
            return self._extract_banking_upi_transactions(tree, max_layer)
    
    def _extract_with_custom_logic(self, tree, extraction_type: str, max_layer: int) -> List[Dict[str, Any]]:
        """Extract transactions using custom logic based on extraction type"""
        if extraction_type == "card":
            return self._extract_card_transactions(tree, max_layer)
        elif extraction_type == "banking_upi":
            return self._extract_banking_upi_transactions(tree, max_layer)
        else:
            # Default extraction
            return self._extract_banking_upi_transactions(tree, max_layer)
    
    def _extract_banking_upi_transactions(self, tree, max_layer: Optional[int]) -> List[Dict[str, Any]]:
        """Extract banking/UPI transactions using enhanced three-table processing system"""
        transactions = []

        # Extract from all three tables as per new requirements
        main_table_transactions = self._extract_main_table_transactions(tree, max_layer)
        victim_table_transactions = self._extract_victim_table_transactions(tree, max_layer)
        suspect_table_transactions = self._extract_suspect_table_transactions(tree, max_layer)

        # Apply enhanced layer logic for intermediate transactions
        enhanced_transactions = self._apply_enhanced_layer_logic(
            main_table_transactions,
            victim_table_transactions,
            suspect_table_transactions
        )

        # Extract suspect details metadata
        suspect_metadata = self._extract_suspect_details_metadata(tree)

        # Combine all transactions
        transactions.extend(enhanced_transactions)

        # Add suspect metadata to the first transaction for reference
        if transactions and suspect_metadata:
            transactions[0]["suspect_metadata"] = suspect_metadata

        # Organize transactions by their relationships (Money Transfer to sub-transactions)
        organized_transactions = self.organize_transactions_by_relationships(transactions)

        self.logger.info(f"Enhanced extraction completed: {len(organized_transactions)} total transactions")
        return organized_transactions

    def _extract_main_table_transactions(self, tree, max_layer: Optional[int]) -> List[Dict[str, Any]]:
        """Extract transactions from Main/Action Taken by Bank/Root Table using complete website logic"""
        transactions = []

        # Find the main transaction table
        main_table = tree.xpath("//table[@id='ContentPlaceHolder1_gedotherbank']")
        if not main_table:
            self.logger.warning("Main transaction table not found for Banking/UPI extraction")
            return transactions

        # Get all rows from the table (skip header)
        rows = main_table[0].xpath('.//tr')[1:] if main_table else []
        if not rows:
            self.logger.warning("No rows found in main transaction table for Banking/UPI extraction")
            return transactions

        # Process each row using website extraction logic
        for row_idx, row in enumerate(rows):
            try:
                # Initialize transaction object with required fields
                transaction = {
                    "fraud_type": "banking_upi",
                }

                # 1. Sender bank account - use row-specific XPath
                sender_account_id = f"ContentPlaceHolder1_gedotherbank_lblppprootaccntidmany_{row_idx}"
                sender_account_elements = row.xpath(f".//span[@id='{sender_account_id}']/text()")
                if not sender_account_elements:
                    sender_account_elements = tree.xpath(f"//span[@id='{sender_account_id}']/text()")
                transaction["sender_account"] = self._clean_account_number(
                    self._clean_text(sender_account_elements[0]) if sender_account_elements else ""
                )

                # 2. Sender transaction ID
                sender_txn_id = f"ContentPlaceHolder1_gedotherbank_lblpproottransidmany_{row_idx}"
                sender_txn_elements = row.xpath(f".//span[@id='{sender_txn_id}']/text()")
                if not sender_txn_elements:
                    sender_txn_elements = tree.xpath(f"//span[@id='{sender_txn_id}']/text()")
                transaction["sender_transaction_id"] = self._clean_text(sender_txn_elements[0]) if sender_txn_elements else ""

                # 3. Sender bank name
                sender_bank_id = f"ContentPlaceHolder1_gedotherbank_lblbankname_{row_idx}"
                sender_bank_elements = row.xpath(f".//span[@id='{sender_bank_id}']/text()")
                if not sender_bank_elements:
                    sender_bank_elements = tree.xpath(f"//span[@id='{sender_bank_id}']/text()")
                transaction["sender_bank"] = self._clean_text(sender_bank_elements[0]) if sender_bank_elements else ""

                # 4. Layer extraction
                all_layer_spans = tree.xpath("//span[contains(@id, 'ContentPlaceHolder1_gedotherbank_lblplayers')]")
                transaction["layer"] = 0  # Default to integer 0

                for span in all_layer_spans:
                    span_id = span.get('id', '')
                    id_match = re.search(r'lblplayers_(\d+)', span_id) # Corrected regex for id_match
                    if id_match and id_match.group(1) == str(row_idx):
                        layer_text = self._clean_text(span.text_content())
                        layer_match = re.search(r'\d+', layer_text)
                        if layer_match:
                            transaction["layer"] = int(layer_match.group(0)) # Store as integer
                            self.logger.info(f"Found layer {transaction['layer']} (type: {type(transaction['layer'])}) for row {row_idx}")
                            break

                # Apply max_layer filtering
                if max_layer is not None:
                    # layer is already an int or 0, so direct comparison is safe
                    if transaction["layer"] > max_layer:
                        continue

                # 5. Transaction type
                type_id = f"ContentPlaceHolder1_gedotherbank_lblottype_{row_idx}"
                type_elements = row.xpath(f".//span[@id='{type_id}']/text()")
                if not type_elements:
                    type_elements = tree.xpath(f"//span[@id='{type_id}']/text()")
                transaction["txn_type"] = self._clean_text(type_elements[0]) if type_elements else ""
                transaction["type"] = transaction["txn_type"]  # Backward compatibility

                # 6. Transaction date - extract from div with "Txn Date:" in the cell
                type_element = row.xpath(f".//span[@id='{type_id}']")
                if type_element:
                    cell = type_element[0].xpath("./ancestor::td[1]")
                    if cell:
                        txn_date_div = cell[0].xpath(".//div[contains(., 'Txn Date:')]")
                        if txn_date_div:
                            date_text = self._clean_text(txn_date_div[0].text_content())
                            date_text = re.sub(r'^Txn\s*Date:\s*', '', date_text)
                            date_match = re.search(r'\d{1,2}[/.-]\d{1,2}[/.-]\d{2,4}(\s+\d{1,2}:\d{1,2}:\d{1,2}\s*(?:AM|PM)?)?', date_text)
                            transaction["date"] = date_match.group(0) if date_match else date_text.strip()
                        else:
                            date_div = cell[0].xpath(".//div[contains(., 'Date:')]")
                            if date_div:
                                date_text = self._clean_text(date_div[0].text_content())
                                date_text = re.sub(r'^Date:\s*', '', date_text)
                                date_match = re.search(r'\d{1,2}[/.-]\d{1,2}[/.-]\d{2,4}(\s+\d{1,2}:\d{1,2}:\d{1,2}\s*(?:AM|PM)?)?', date_text)
                                transaction["date"] = date_match.group(0) if date_match else date_text.strip()
                            else:
                                cell_text = cell[0].text_content()
                                date_match = re.search(r'\d{1,2}[/.-]\d{1,2}[/.-]\d{2,4}(\s+\d{1,2}:\d{1,2}:\d{1,2}\s*(?:AM|PM)?)?', cell_text)
                                transaction["date"] = date_match.group(0) if date_match else ""
                    else:
                        transaction["date"] = ""
                else:
                    transaction["date"] = ""

                # 7. Receiver bank name - extract from 4th cell
                bank_cell = row.xpath('.//td[4]')
                if bank_cell:
                    bank_spans = bank_cell[0].xpath('.//span[1]/text()')
                    if bank_spans:
                        transaction["receiver_bank"] = self._clean_text(bank_spans[0])
                    else:
                        cell_text = self._clean_text(bank_cell[0].text_content())
                        if cell_text:
                            transaction["receiver_bank"] = cell_text
                        else:
                            bank_id = f"ContentPlaceHolder1_gedotherbank_lblbanknamea_{row_idx}"
                            bank_elements = row.xpath(f".//span[@id='{bank_id}']/text()")
                            transaction["receiver_bank"] = self._clean_text(bank_elements[0]) if bank_elements else ""
                else:
                    transaction["receiver_bank"] = ""

                # 8. Receiver bank account
                receiver_account_id = f"ContentPlaceHolder1_gedotherbank_lblacounta_{row_idx}"
                receiver_account_elements = row.xpath(f".//span[@id='{receiver_account_id}']/text()")
                if not receiver_account_elements:
                    receiver_account_elements = tree.xpath(f"//span[@id='{receiver_account_id}']/text()")

                if not receiver_account_elements:
                    # Fallback to merchant info extraction
                    account_cell = row.xpath('.//td[6]')
                    if account_cell and len(account_cell) > 0:
                        merchant_info = self._extract_merchant_info(account_cell[0])
                        transaction["receiver_account"] = merchant_info if merchant_info else ""
                    else:
                        transaction["receiver_account"] = ""
                else:
                    transaction["receiver_account"] = self._clean_account_number(
                        self._clean_text(receiver_account_elements[0]) if receiver_account_elements else ""
                    )

                # 9. Receiver transaction ID
                receiver_txn_id = f"ContentPlaceHolder1_gedotherbank_lbltransid_{row_idx}"
                receiver_txn_elements = row.xpath(f".//span[@id='{receiver_txn_id}']/text()")
                if not receiver_txn_elements:
                    receiver_txn_elements = tree.xpath(f"//span[@id='{receiver_txn_id}']/text()")
                transaction["receiver_transaction_id"] = self._clean_text(receiver_txn_elements[0]) if receiver_txn_elements else ""

                # 10. Transaction amount
                amount_id = f"ContentPlaceHolder1_gedotherbank_lbloamnt_{row_idx}"
                amount_elements = row.xpath(f".//span[@id='{amount_id}']/text()")
                if not amount_elements:
                    amount_elements = tree.xpath(f"//span[@id='{amount_id}']/text()")
                amount_text = self._clean_text(amount_elements[0]) if amount_elements else ""

                if amount_text:
                    amount_text = amount_text.replace('₹', '').replace('Rs.', '').replace('Rs', '').replace(',', '').strip()
                    amount_match = re.search(r'\d+(\.\d+)?', amount_text)
                    transaction["amount"] = amount_match.group(0) if amount_match else amount_text
                else:
                    transaction["amount"] = ""

                # 11. Receiver IFSC code - Enhanced extraction
                receiver_ifsc_id = f"ContentPlaceHolder1_gedotherbank_lblifsc_{row_idx}"
                receiver_ifsc_elements = row.xpath(f".//span[@id='{receiver_ifsc_id}']/text()")
                if not receiver_ifsc_elements:
                    receiver_ifsc_elements = tree.xpath(f"//span[@id='{receiver_ifsc_id}']/text()")

                # If IFSC not found by ID, try to extract from next div after receiver account
                if not receiver_ifsc_elements:
                    receiver_account_element = row.xpath(f".//span[@id='{receiver_account_id}']")
                    if receiver_account_element:
                        # Look for IFSC in the next div element
                        next_divs = receiver_account_element[0].xpath("./following::div[1]")
                        if next_divs:
                            ifsc_text = self._clean_text(next_divs[0].text_content())
                            # Extract IFSC pattern (typically 11 characters: 4 letters + 7 digits)
                            ifsc_match = re.search(r'[A-Z]{4}[0-9]{7}', ifsc_text.upper())
                            if ifsc_match:
                                transaction["receiver_ifsc"] = ifsc_match.group(0)
                            else:
                                transaction["receiver_ifsc"] = ifsc_text if len(ifsc_text) <= 15 else ""
                        else:
                            transaction["receiver_ifsc"] = ""
                    else:
                        transaction["receiver_ifsc"] = ""
                else:
                    transaction["receiver_ifsc"] = self._clean_text(receiver_ifsc_elements[0])

                # 12. Reference information (combining columns 7, 8, 9)
                reference_text = self._extract_reference_info(row, row_idx)
                transaction["receiver_info"] = reference_text
                transaction["reference"] = reference_text  # For compatibility

                # 13. Additional metadata fields
                transaction["sr_no"] = str(row_idx + 1)
                transaction["extracted_at"] = datetime.now().isoformat()
                transaction["source"] = "html_extraction"

                # 14. Validation flags
                transaction["is_valid"] = str(bool(transaction["sender_account"] and transaction["amount"]))
                validation_errors = []

                if not transaction["sender_account"]:
                    validation_errors.append("Missing sender account")
                if not transaction["amount"]:
                    validation_errors.append("Missing amount")
                if not transaction["date"]:
                    validation_errors.append("Missing date")

                transaction["validation_errors"] = ", ".join(validation_errors) if validation_errors else ""

                # Skip rows with missing critical data
                if not transaction["sender_account"] or not transaction["amount"]:
                    self.logger.warning(f"Skipping row {row_idx} due to missing critical data: {validation_errors}")
                    continue

                # Skip transactions with low amounts
                if self._should_skip_transaction_by_amount(transaction["amount"]):
                    self.logger.info(f"Skipping row {row_idx} due to low amount: {transaction['amount']}")
                    continue

                # Check for duplicates
                is_duplicate = self._is_duplicate_transaction(transaction, transactions)
                if not is_duplicate:
                    transactions.append(transaction)
                else:
                    self.logger.info(f"Skipping duplicate transaction for row {row_idx}")

            except Exception as e:
                self.logger.error(f"Error processing Banking/UPI transaction row {row_idx}: {str(e)}")
                continue

        self.logger.info(f"Extracted {len(transactions)} Banking/UPI transactions")
        return transactions
    
    def _extract_card_transactions(self, tree, max_layer: Optional[int]) -> List[Dict[str, Any]]:
        """Extract card transactions using complete website logic with specific element IDs"""
        transactions = []

        # Find the main transaction table
        main_table = tree.xpath("//table[@id='ContentPlaceHolder1_gedotherbank']")
        if not main_table:
            self.logger.warning("Main transaction table not found for card extraction")
            return transactions

        # Get all rows from the table (skip header)
        rows = main_table[0].xpath('.//tr')[1:] if main_table else []
        if not rows:
            self.logger.warning("No rows found in main transaction table for card extraction")
            return transactions

        # Process each row using website extraction logic for card transactions
        for row_idx, row in enumerate(rows):
            try:
                # Initialize transaction object with card-specific fields
                transaction = {
                    "fraud_type": "card",
                }

                # Extract card-specific data using similar logic to banking/UPI but adapted for cards
                # 1. Card number (equivalent to sender account)
                card_id = f"ContentPlaceHolder1_gedotherbank_lblcardno_{row_idx}"
                card_elements = row.xpath(f".//span[@id='{card_id}']/text()")
                if not card_elements:
                    card_elements = tree.xpath(f"//span[@id='{card_id}']/text()")
                transaction["sender_account"] = self._clean_text(card_elements[0]) if card_elements else ""
                transaction["card_number"] = transaction["sender_account"]  # For compatibility

                # 2. Transaction ID
                txn_id = f"ContentPlaceHolder1_gedotherbank_lbltxnid_{row_idx}"
                txn_elements = row.xpath(f".//span[@id='{txn_id}']/text()")
                if not txn_elements:
                    txn_elements = tree.xpath(f"//span[@id='{txn_id}']/text()")
                transaction["sender_transaction_id"] = self._clean_text(txn_elements[0]) if txn_elements else ""
                transaction["transaction_id"] = transaction["sender_transaction_id"]  # For compatibility

                # 3. Bank name (card issuing bank)
                bank_id = f"ContentPlaceHolder1_gedotherbank_lblbankname_{row_idx}"
                bank_elements = row.xpath(f".//span[@id='{bank_id}']/text()")
                if not bank_elements:
                    bank_elements = tree.xpath(f"//span[@id='{bank_id}']/text()")
                transaction["sender_bank"] = self._clean_text(bank_elements[0]) if bank_elements else ""

                # 4. Layer extraction (same logic as banking/UPI)
                all_layer_spans = tree.xpath("//span[contains(@id, 'ContentPlaceHolder1_gedotherbank_lblplayers')]")
                transaction["layer"] = 0  # Default to integer 0

                for span in all_layer_spans:
                    span_id = span.get('id', '')
                    id_match = re.search(r'lblplayers_(\d+)', span_id) # Corrected regex for id_match
                    if id_match and id_match.group(1) == str(row_idx):
                        layer_text = self._clean_text(span.text_content())
                        layer_match = re.search(r'\d+', layer_text)
                        if layer_match:
                            transaction["layer"] = int(layer_match.group(0)) # Store as integer
                            self.logger.info(f"Found layer {transaction['layer']} (type: {type(transaction['layer'])}) for row {row_idx}")
                            break

                # Apply max_layer filtering
                if max_layer is not None:
                    # layer is already an int or 0, so direct comparison is safe
                    if transaction["layer"] > max_layer:
                        continue

                # 5. Transaction type
                type_id = f"ContentPlaceHolder1_gedotherbank_lbltxntype_{row_idx}"
                type_elements = row.xpath(f".//span[@id='{type_id}']/text()")
                if not type_elements:
                    type_elements = tree.xpath(f"//span[@id='{type_id}']/text()")
                transaction["txn_type"] = self._clean_text(type_elements[0]) if type_elements else ""
                transaction["type"] = transaction["txn_type"]

                # 6. Transaction date
                date_id = f"ContentPlaceHolder1_gedotherbank_lbltxndate_{row_idx}"
                date_elements = row.xpath(f".//span[@id='{date_id}']/text()")
                if not date_elements:
                    date_elements = tree.xpath(f"//span[@id='{date_id}']/text()")
                transaction["date"] = self._clean_text(date_elements[0]) if date_elements else ""

                # 7. Merchant name (receiver)
                merchant_id = f"ContentPlaceHolder1_gedotherbank_lblmerchant_{row_idx}"
                merchant_elements = row.xpath(f".//span[@id='{merchant_id}']/text()")
                if not merchant_elements:
                    merchant_elements = tree.xpath(f"//span[@id='{merchant_id}']/text()")
                transaction["receiver_account"] = self._clean_text(merchant_elements[0]) if merchant_elements else ""
                transaction["merchant_name"] = transaction["receiver_account"]  # For compatibility

                # 8. Transaction amount
                amount_id = f"ContentPlaceHolder1_gedotherbank_lblamount_{row_idx}"
                amount_elements = row.xpath(f".//span[@id='{amount_id}']/text()")
                if not amount_elements:
                    amount_elements = tree.xpath(f"//span[@id='{amount_id}']/text()")
                amount_text = self._clean_text(amount_elements[0]) if amount_elements else ""

                if amount_text:
                    amount_text = amount_text.replace('₹', '').replace('Rs.', '').replace('Rs', '').replace(',', '').strip()
                    amount_match = re.search(r'\d+(\.\d+)?', amount_text)
                    transaction["amount"] = amount_match.group(0) if amount_match else amount_text
                else:
                    transaction["amount"] = ""

                # 9. Reference information
                reference_text = self._extract_reference_info(row, row_idx)
                transaction["receiver_info"] = reference_text
                transaction["reference"] = reference_text

                # 10. Additional card-specific fields
                transaction["receiver_ifsc"] = ""  # Not applicable for card transactions
                transaction["sr_no"] = str(row_idx + 1)
                transaction["extracted_at"] = datetime.now().isoformat()
                transaction["source"] = "html_extraction"

                # 11. Validation flags
                transaction["is_valid"] = str(bool(transaction["sender_account"] and transaction["amount"]))
                validation_errors = []

                if not transaction["sender_account"]:
                    validation_errors.append("Missing card number")
                if not transaction["amount"]:
                    validation_errors.append("Missing amount")
                if not transaction["date"]:
                    validation_errors.append("Missing date")

                transaction["validation_errors"] = ", ".join(validation_errors) if validation_errors else ""

                # Skip rows with missing critical data
                if not transaction["sender_account"] or not transaction["amount"]:
                    self.logger.warning(f"Skipping card transaction row {row_idx} due to missing critical data: {validation_errors}")
                    continue

                # Skip transactions with low amounts
                if self._should_skip_transaction_by_amount(transaction["amount"]):
                    self.logger.info(f"Skipping card transaction row {row_idx} due to low amount: {transaction['amount']}")
                    continue

                # Check for duplicates
                is_duplicate = self._is_duplicate_transaction(transaction, transactions)
                if not is_duplicate:
                    transactions.append(transaction)
                else:
                    self.logger.info(f"Skipping duplicate card transaction for row {row_idx}")

            except Exception as e:
                self.logger.error(f"Error processing card transaction row {row_idx}: {str(e)}")
                continue

        self.logger.info(f"Extracted {len(transactions)} card transactions")
        return transactions
    
    def _determine_layer(self, row_index: int) -> int:
        """Determine transaction layer based on row index"""
        # Simple layer determination logic
        # This can be enhanced based on specific business rules
        return min(row_index // 10, 7)  # Max layer 7
    
    def _process_layer_transactions(self, transactions, max_layer):
        try:
            final_max_layer = int(max_layer)
        except (ValueError, TypeError):
            final_max_layer = 7  # fallback default

        layer_transactions = {f"Layer {i}": [] for i in range(final_max_layer + 1)}

        for transaction in transactions:
            layer_raw = transaction.get("layer", 0)
            try:
                # Always force conversion to int, log and skip if not possible
                layer_as_int = int(layer_raw)
            except Exception as e:
                self.logger.error(f"Could not convert layer '{layer_raw}' to int: {e}. Skipping transaction.")
                continue

            # Now both are guaranteed ints
            if layer_as_int <= final_max_layer:
                layer_transactions[f"Layer {layer_as_int}"].append(transaction)
            else:
                self.logger.warning(f"layer_as_int {layer_as_int} > final_max_layer {final_max_layer}, skipping.")

        return layer_transactions
    
    def _generate_bank_notice_data(self, transactions: List[Dict[str, Any]], metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Generate bank notice data structure"""
        # Group transactions by bank
        banks = {}
        for transaction in transactions:
            bank_name = transaction.get("bank_name", "Unknown Bank")
            if bank_name not in banks:
                banks[bank_name] = []
            banks[bank_name].append(transaction)
        
        return {
            "banks": banks,
            "total_banks": len(banks),
            "total_amount": sum(float(t.get("amount", 0) or 0) for t in transactions),
            "metadata": metadata
        }
    
    def _generate_graph_data(self, transactions: List[Dict[str, Any]], metadata: Dict[str, Any], max_layer: int) -> Dict[str, Any]:
        """Generate comprehensive graph data for visualization with proper node hierarchy and consolidation"""
        nodes = []
        edges = []

        # Track unique accounts and their consolidated transaction data
        sender_accounts = {}  # Layer 0 nodes (fraud sources)
        receiver_accounts = {}  # Layer 1+ nodes (destinations)

        # First pass: Consolidate transactions by account
        for transaction in transactions:
            sender_account = transaction.get("sender_account")
            receiver_account = transaction.get("receiver_account")
            layer = int(transaction.get("layer", 0))

            # Process sender accounts (always layer 0 - fraud sources)
            if sender_account:
                if sender_account not in sender_accounts:
                    sender_accounts[sender_account] = {
                        "id": sender_account,
                        "label": transaction.get("sender_bank", sender_account[:20] + "..." if len(sender_account) > 20 else sender_account),
                        "account": sender_account,
                        "type": "sender_account",
                        "layer": 0,  # Sender nodes are always at layer 0
                        "bank": transaction.get("sender_bank", ""),
                        "sender_bank": transaction.get("sender_bank", ""),
                        "fraud_type": transaction.get("fraud_type", ""),
                        "isSenderNode": True,
                        "isVictim": True,
                        "transactions": [],  # Store all transactions from this account
                        "total_amount": 0,
                        "transaction_count": 0
                    }

                # Add transaction to sender account
                sender_accounts[sender_account]["transactions"].append({
                    "txn_id": transaction.get("sender_transaction_id", ""),
                    "transaction_id": transaction.get("sender_transaction_id", ""),
                    "amount": transaction.get("amount", ""),
                    "date": transaction.get("date", ""),
                    "txn_type": transaction.get("txn_type", ""),
                    "reference": transaction.get("reference", ""),
                    "receiver_info": transaction.get("receiver_info", ""),
                    "receiver_account": receiver_account,
                    "receiver_bank": transaction.get("receiver_bank", ""),
                    "layer": layer
                })

                # Update totals
                try:
                    amount = float(transaction.get("amount", 0) or 0)
                    sender_accounts[sender_account]["total_amount"] += amount
                except (ValueError, TypeError):
                    pass
                sender_accounts[sender_account]["transaction_count"] += 1

            # Process receiver accounts (layer 1+ - destinations)
            if receiver_account:
                receiver_layer = max(1, layer)  # Receivers are always layer 1+

                if receiver_account not in receiver_accounts:
                    receiver_accounts[receiver_account] = {
                        "id": receiver_account,
                        "label": transaction.get("receiver_bank", receiver_account[:20] + "..." if len(receiver_account) > 20 else receiver_account),
                        "account": receiver_account,
                        "type": "receiver_account",
                        "layer": receiver_layer,
                        "bank": transaction.get("receiver_bank", ""),
                        "receiver_bank": transaction.get("receiver_bank", ""),
                        "receiver_ifsc": transaction.get("receiver_ifsc", ""),
                        "fraud_type": transaction.get("fraud_type", ""),
                        "isSenderNode": False,
                        "isMainTransaction": True,
                        "transactions": [],  # Store all transactions to this account
                        "total_amount": 0,
                        "transaction_count": 0
                    }

                # Add transaction to receiver account
                receiver_accounts[receiver_account]["transactions"].append({
                    "txn_id": transaction.get("receiver_transaction_id", ""),
                    "transaction_id": transaction.get("receiver_transaction_id", ""),
                    "amount": transaction.get("amount", ""),
                    "date": transaction.get("date", ""),
                    "txn_type": transaction.get("txn_type", ""),
                    "reference": transaction.get("reference", ""),
                    "receiver_info": transaction.get("receiver_info", ""),
                    "sender_account": sender_account,
                    "sender_bank": transaction.get("sender_bank", ""),
                    "layer": layer
                })

                # Update totals
                try:
                    amount = float(transaction.get("amount", 0) or 0)
                    receiver_accounts[receiver_account]["total_amount"] += amount
                except (ValueError, TypeError):
                    pass
                receiver_accounts[receiver_account]["transaction_count"] += 1

        # Second pass: Create consolidated nodes
        # Add sender nodes (layer 0)
        for account, node_data in sender_accounts.items():
            # Set primary transaction data from the first transaction
            if node_data["transactions"]:
                primary_txn = node_data["transactions"][0]
                node_data.update({
                    "amount": str(node_data["total_amount"]),
                    "date": primary_txn["date"],
                    "txn_id": primary_txn["txn_id"],
                    "transaction_id": primary_txn["txn_id"],
                    "txn_type": primary_txn["txn_type"],
                    "reference": primary_txn["reference"],
                    "receiver_info": primary_txn["receiver_info"],
                    "consolidatedTransactions": node_data["transactions"],
                    "hasSubTransactions": len(node_data["transactions"]) > 1
                })
            nodes.append(node_data)

        # Add receiver nodes (layer 1+)
        for account, node_data in receiver_accounts.items():
            # Set primary transaction data from the first transaction
            if node_data["transactions"]:
                primary_txn = node_data["transactions"][0]
                node_data.update({
                    "amount": str(node_data["total_amount"]),
                    "date": primary_txn["date"],
                    "txn_id": primary_txn["txn_id"],
                    "transaction_id": primary_txn["txn_id"],
                    "txn_type": primary_txn["txn_type"],
                    "reference": primary_txn["reference"],
                    "receiver_info": primary_txn["receiver_info"],
                    "consolidatedTransactions": node_data["transactions"],
                    "hasSubTransactions": len(node_data["transactions"]) > 1
                })
            nodes.append(node_data)

        # Third pass: Create edges between sender and receiver nodes
        edge_connections = set()  # Track unique connections to avoid duplicates

        for transaction in transactions:
            sender_account = transaction.get("sender_account")
            receiver_account = transaction.get("receiver_account")
            layer = int(transaction.get("layer", 0))

            if sender_account and receiver_account:
                # Create unique edge identifier
                connection_key = f"{sender_account}_{receiver_account}"

                if connection_key not in edge_connections:
                    edge_id = f"edge_{sender_account}_{receiver_account}"
                    edges.append({
                        "id": edge_id,
                        "source": sender_account,
                        "target": receiver_account,
                        "amount": transaction.get("amount", "0"),
                        "date": transaction.get("date", ""),
                        "type": "transfer",
                        "layer": layer,
                        "transaction_id": transaction.get("sender_transaction_id", ""),
                        "reference": transaction.get("receiver_info", ""),
                        "fraud_type": transaction.get("fraud_type", ""),
                        "sender_bank": transaction.get("sender_bank", ""),
                        "receiver_bank": transaction.get("receiver_bank", ""),
                        "isParentChild": True
                    })
                    edge_connections.add(connection_key)

        # Calculate graph statistics
        total_amount = 0
        suspicious_nodes = 0
        suspicious_edges = 0

        # Calculate total amount from consolidated nodes
        for node in nodes:
            try:
                amount = float(node.get("total_amount", 0) or 0)
                total_amount += amount

                # Mark high-value accounts as suspicious
                if amount > 100000:
                    suspicious_nodes += 1

            except (ValueError, TypeError):
                pass

        # Mark high-value edges as suspicious
        for edge in edges:
            try:
                amount = float(edge.get("amount", 0) or 0)
                if amount > 50000:
                    suspicious_edges += 1
            except (ValueError, TypeError):
                pass

        return {
            "nodes": nodes,
            "edges": edges,
            "transactions": transactions,
            "metadata": {
                "total_nodes": len(nodes),
                "total_edges": len(edges),
                "max_layer": max_layer,
                "total_amount": total_amount,
                "suspicious_nodes": suspicious_nodes,
                "suspicious_edges": suspicious_edges,
                "sender_accounts": len(sender_accounts),
                "receiver_accounts": len(receiver_accounts),
                "complaint_metadata": metadata
            },
            "max_layer": max_layer
        }

    def _extract_victim_table_transactions(self, tree, max_layer: Optional[int]) -> List[Dict[str, Any]]:
        """Extract transactions from Victim's Account Transaction Table"""
        transactions = []

        # Find the victim transaction table
        victim_table = tree.xpath("//table[@id='ContentPlaceHolder1_gv_ActiontakenStatus']")
        if not victim_table:
            self.logger.warning("Victim transaction table not found")
            return transactions

        # Get all rows from the table (skip header)
        rows = victim_table[0].xpath('.//tr')[1:] if victim_table else []
        if not rows:
            self.logger.warning("No rows found in victim transaction table")
            return transactions

        # Process each row
        for row_idx, row in enumerate(rows):
            try:
                transaction = {
                    "fraud_type": "banking_upi",
                    "table_source": "victim_table",
                }

                # Extract transaction ID
                txn_id = f"ContentPlaceHolder1_gv_ActiontakenStatus_lbltransid_{row_idx}"
                txn_elements = row.xpath(f".//span[@id='{txn_id}']/text()")
                transaction["transaction_id"] = self._clean_text(txn_elements[0]) if txn_elements else ""

                # Extract account number
                account_id = f"ContentPlaceHolder1_gv_ActiontakenStatus_lblaccount_{row_idx}"
                account_elements = row.xpath(f".//span[@id='{account_id}']/text()")
                transaction["account_number"] = self._clean_account_number(
                    self._clean_text(account_elements[0]) if account_elements else ""
                )

                # Extract layer information
                layer_id = f"ContentPlaceHolder1_gv_ActiontakenStatus_lbllayer_{row_idx}"
                layer_elements = row.xpath(f".//span[@id='{layer_id}']/text()")
                layer_text = self._clean_text(layer_elements[0]) if layer_elements else "0"
                layer_match = re.search(r'\d+', layer_text)
                transaction["layer"] = int(layer_match.group(0)) if layer_match else 0 # Store as integer

                # Apply max_layer filtering
                if max_layer is not None:
                    # layer is already an int or 0, so direct comparison is safe
                    if transaction["layer"] > max_layer:
                        continue
                layer_text = self._clean_text(layer_elements[0]) if layer_elements else "0"
                layer_match = re.search(r'\d+', layer_text)
                transaction["layer"] = int(layer_match.group(0)) if layer_match else 0 # Store as integer

                # Apply max_layer filtering
                if max_layer is not None:
                    # layer is already an int or 0, so direct comparison is safe
                    if transaction["layer"] > max_layer:
                        continue

                # Add additional fields for consistency
                transaction["sender_account"] = transaction["account_number"]
                transaction["sender_transaction_id"] = transaction["transaction_id"]
                transaction["sr_no"] = str(row_idx + 1)
                transaction["extracted_at"] = datetime.now().isoformat()
                transaction["source"] = "victim_table_extraction"

                if transaction["account_number"] and transaction["transaction_id"]:
                    transactions.append(transaction)

            except Exception as e:
                self.logger.error(f"Error processing victim table row {row_idx}: {str(e)}")
                continue

        self.logger.info(f"Extracted {len(transactions)} victim table transactions")
        return transactions

    def _extract_suspect_table_transactions(self, tree, max_layer: Optional[int]) -> List[Dict[str, Any]]:
        """Extract transactions from Suspect Account Table"""
        transactions = []

        # Find the suspect transaction table
        suspect_table = tree.xpath("//table[@id='ContentPlaceHolder1_gv_ActiontakenStatuscred']")
        if not suspect_table:
            self.logger.warning("Suspect transaction table not found")
            return transactions

        # Get all rows from the table (skip header)
        rows = suspect_table[0].xpath('.//tr')[1:] if suspect_table else []
        if not rows:
            self.logger.warning("No rows found in suspect transaction table")
            return transactions

        # Process each row
        for row_idx, row in enumerate(rows):
            try:
                transaction = {
                    "fraud_type": "banking_upi",
                    "table_source": "suspect_table",
                }

                # Extract layer information
                layer_id = f"ContentPlaceHolder1_gv_ActiontakenStatuscred_lblplayers_{row_idx}"
                layer_elements = row.xpath(f".//span[@id='{layer_id}']/text()")
                layer_text = self._clean_text(layer_elements[0]) if layer_elements else "0"
                layer_match = re.search(r'\d+', layer_text)
                transaction["layer"] = int(layer_match.group(0)) if layer_match else 0  # Store as integer

                # Extract transaction ID
                txn_id = f"ContentPlaceHolder1_gv_ActiontakenStatuscred_Label5_{row_idx}"
                txn_elements = row.xpath(f".//span[@id='{txn_id}']/text()")
                transaction["transaction_id"] = self._clean_text(txn_elements[0]) if txn_elements else ""

                # Extract account number
                account_id = f"ContentPlaceHolder1_gv_ActiontakenStatus_lblaccount_{row_idx}"
                account_elements = row.xpath(f".//span[@id='{account_id}']/text()")
                transaction["account_number"] = self._clean_account_number(
                    self._clean_text(account_elements[0]) if account_elements else ""
                )

                # Extract bank name
                bank_id = f"ContentPlaceHolder1_gv_ActiontakenStatuscred_Label13_{row_idx}"
                bank_elements = row.xpath(f".//span[@id='{bank_id}']/text()")
                transaction["bank_name"] = self._clean_text(bank_elements[0]) if bank_elements else ""

                # Apply max_layer filtering
                if max_layer is not None:
                    try:
                        layer_value = int(transaction["layer"]) if transaction["layer"] else 0
                        if layer_value > max_layer:
                            continue
                    except (ValueError, TypeError):
                        continue

                # Add additional fields for consistency
                transaction["sender_account"] = transaction["account_number"]
                transaction["sender_transaction_id"] = transaction["transaction_id"]
                transaction["sender_bank"] = transaction["bank_name"]
                transaction["sr_no"] = str(row_idx + 1)
                transaction["extracted_at"] = datetime.now().isoformat()
                transaction["source"] = "suspect_table_extraction"
                transaction["is_suspect_account"] = "true"

                if transaction["account_number"] and transaction["transaction_id"]:
                    transactions.append(transaction)

            except Exception as e:
                self.logger.error(f"Error processing suspect table row {row_idx}: {str(e)}")
                continue

        self.logger.info(f"Extracted {len(transactions)} suspect table transactions")
        return transactions

    def _apply_enhanced_layer_logic(
        self,
        main_transactions: List[Dict[str, Any]],
        victim_transactions: List[Dict[str, Any]],
        suspect_transactions: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Apply enhanced layer logic to insert suspect table data as intermediate layers
        when main table layer doesn't start with 1
        """
        enhanced_transactions = []

        # Check if main table starts with layer 2+
        main_starts_with_higher_layer = False
        if main_transactions:
            first_main_layer = int(main_transactions[0].get("layer", "0"))
            if first_main_layer > 1:
                main_starts_with_higher_layer = True
                self.logger.info(f"Main table starts with layer {first_main_layer}, checking for Layer 1 in suspect table")

        if main_starts_with_higher_layer and suspect_transactions:
            # Find Layer 1 transactions in suspect table
            layer_1_suspects = [t for t in suspect_transactions if int(t.get("layer", "0")) == 1]

            if layer_1_suspects:
                self.logger.info(f"Found {len(layer_1_suspects)} Layer 1 transactions in suspect table")

                # Match criteria between tables
                matched_suspects = []
                for suspect_txn in layer_1_suspects:
                    for main_txn in main_transactions:
                        if self._transactions_match(suspect_txn, main_txn, victim_transactions):
                            # Mark as intermediate layer with "Suspect Account" badge
                            suspect_txn["is_intermediate_layer"] = "true"
                            suspect_txn["account_badge"] = "Suspect Account"
                            matched_suspects.append(suspect_txn)
                            self.logger.info(f"Matched suspect transaction: {suspect_txn.get('transaction_id', 'N/A')}")
                            break

                # Add matched suspects as intermediate layers
                enhanced_transactions.extend(matched_suspects)

        # Add all main transactions
        enhanced_transactions.extend(main_transactions)

        # Add victim transactions if any
        enhanced_transactions.extend(victim_transactions)

        # Sort by layer for proper ordering
        enhanced_transactions.sort(key=lambda x: int(x.get("layer", "0")))

        self.logger.info(f"Enhanced layer logic applied: {len(enhanced_transactions)} total transactions")
        return enhanced_transactions

    def _transactions_match(
        self,
        suspect_txn: Dict[str, Any],
        main_txn: Dict[str, Any],
        victim_transactions: List[Dict[str, Any]]
    ) -> bool:
        """
        Check if suspect transaction matches main transaction based on criteria
        """
        # Extract numerical part of transaction IDs for comparison
        suspect_txn_id_num = self._extract_numerical_id(suspect_txn.get("transaction_id", ""))
        main_txn_id_num = self._extract_numerical_id(main_txn.get("sender_transaction_id", ""))

        # Check transaction ID match
        txn_id_match = suspect_txn_id_num and main_txn_id_num and suspect_txn_id_num == main_txn_id_num

        # Check account number match
        account_match = (
            suspect_txn.get("account_number") == main_txn.get("sender_account") and
            suspect_txn.get("account_number") is not None
        )

        # Check bank name match
        bank_match = (
            suspect_txn.get("bank_name") == main_txn.get("sender_bank") and
            suspect_txn.get("bank_name") is not None
        )

        # Check against victim transactions for additional validation
        victim_match = False
        for victim_txn in victim_transactions:
            victim_txn_id_num = self._extract_numerical_id(victim_txn.get("transaction_id", ""))
            if victim_txn_id_num and (victim_txn_id_num == suspect_txn_id_num or victim_txn_id_num == main_txn_id_num):
                victim_match = True
                break

        # Return true if we have strong matches
        match_score = sum([
            1 if txn_id_match else 0,
            1 if account_match else 0,
            1 if bank_match else 0,
            1 if victim_match else 0
        ])
        is_match = match_score >= 2  # Require at least 2 matching criteria

        if is_match:
            self.logger.info(
                f"Transaction match found - TxnID: {txn_id_match}, Account: {account_match}, "
                f"Bank: {bank_match}, Victim: {victim_match}"
            )

        return is_match

    def _extract_numerical_id(self, transaction_id: str) -> Optional[str]:
        """Extract numerical part from transaction ID, ignoring alphabetic characters"""
        if not transaction_id:
            return None

        # Extract all digits from the transaction ID
        numerical_part = re.sub(r'[^0-9]', '', transaction_id)
        return numerical_part if numerical_part else None

    def _extract_suspect_details_metadata(self, tree) -> Dict[str, Any]:
        """Extract complete suspect details table metadata"""
        suspect_metadata = {
            "suspect_details": [],
            "extracted_at": datetime.now().isoformat()
        }

        # Find the suspect details table
        suspect_table = tree.xpath("//table[@id='ContentPlaceHolder1_repsusdetail']")
        if not suspect_table:
            self.logger.warning("Suspect details table not found")
            return suspect_metadata

        # Get all rows from the table
        rows = suspect_table[0].xpath('.//tr')
        if not rows:
            self.logger.warning("No rows found in suspect details table")
            return suspect_metadata

        # Process each row to extract suspect details
        for row_idx, row in enumerate(rows):
            try:
                # Extract all cells in the row
                cells = row.xpath('.//td')
                if not cells:
                    continue

                row_data = []
                for cell in cells:
                    cell_text = self._clean_text(cell.text_content())
                    row_data.append(cell_text)

                if row_data:  # Only add non-empty rows
                    suspect_metadata["suspect_details"].append({
                        "row_index": row_idx,
                        "data": row_data
                    })

            except Exception as e:
                self.logger.error(f"Error processing suspect details row {row_idx}: {str(e)}")
                continue

        self.logger.info(f"Extracted suspect details metadata: {len(suspect_metadata['suspect_details'])} rows")
        return suspect_metadata

    def _extract_merchant_info(self, cell_element) -> str:
        """Extract merchant information from a cell element"""
        try:
            cell_text = self._clean_text(cell_element.text_content())

            # Look for merchant patterns
            merchant_patterns = [
                r'Merchant[:\s]+([^,\n]+)',
                r'To[:\s]+([^,\n]+)',
                r'Payee[:\s]+([^,\n]+)',
                r'Beneficiary[:\s]+([^,\n]+)'
            ]

            for pattern in merchant_patterns:
                match = re.search(pattern, cell_text, re.IGNORECASE)
                if match:
                    return match.group(1).strip()

            # If no specific pattern found, return cleaned cell text if it looks like merchant info
            if cell_text and len(cell_text) > 3 and not cell_text.isdigit():
                return cell_text

            return ""
        except Exception as e:
            self.logger.error(f"Error extracting merchant info: {str(e)}")
            return ""

    def _extract_reference_info(self, row, row_idx: int) -> str:
        """Extract reference information from columns 7, 8, and 9"""
        try:
            reference_text = ""

            # Extract column 7 (usually cheque number or reference number)
            col7_cell = row.xpath('.//td[7]')
            col7_text = self._clean_text(col7_cell[0].text_content()) if col7_cell else ""

            # Extract column 8 (usually remarks or additional information)
            col8_cell = row.xpath('.//td[8]')
            col8_text = self._clean_text(col8_cell[0].text_content()) if col8_cell else ""

            # Extract column 9 (usually additional remarks)
            col9_cell = row.xpath('.//td[9]')
            col9_text = self._clean_text(col9_cell[0].text_content()) if col9_cell else ""

            # Also try to extract using specific element IDs as a backup
            # Try to extract cheque number
            cheque_id = f"ContentPlaceHolder1_gedotherbank_lblchequeno_{row_idx}"
            cheque_elements = row.xpath(f".//span[@id='{cheque_id}']/text()")
            cheque_number = self._clean_text(cheque_elements[0]) if cheque_elements else ""

            # Try to extract remarks or additional information
            remarks_id = f"ContentPlaceHolder1_gedotherbank_lblremarks_{row_idx}"
            remarks_elements = row.xpath(f".//span[@id='{remarks_id}']/text()")
            remarks = self._clean_text(remarks_elements[0]) if remarks_elements else ""

            # Try to extract reference number
            ref_id = f"ContentPlaceHolder1_gedotherbank_lblrefno_{row_idx}"
            ref_elements = row.xpath(f".//span[@id='{ref_id}']/text()")
            ref_number = self._clean_text(ref_elements[0]) if ref_elements else ""

            # Combine all reference information into a single string
            # Use cheque number if available, otherwise use column 7 text
            if cheque_number:
                reference_text += cheque_number
            elif col7_text:
                reference_text += col7_text

            # Add remarks or column 8 text if available
            if remarks and remarks not in reference_text:
                reference_text += " " + remarks if reference_text else remarks
            elif col8_text and col8_text not in reference_text:
                reference_text += " " + col8_text if reference_text else col8_text

            # Add column 9 text if available and not already included
            if col9_text and col9_text not in reference_text:
                reference_text += " " + col9_text if reference_text else col9_text

            # Add reference number if available and not already included
            if ref_number and ref_number not in reference_text:
                reference_text += " " + ref_number if reference_text else ref_number

            return reference_text.strip()

        except Exception as e:
            self.logger.error(f"Error extracting reference info for row {row_idx}: {str(e)}")
            return ""

    def _should_skip_transaction_by_amount(self, amount_str: str) -> bool:
        """Check if transaction should be skipped based on amount threshold"""
        try:
            if not amount_str:
                return True

            # Clean amount string
            amount_clean = amount_str.replace('₹', '').replace('Rs.', '').replace('Rs', '').replace(',', '').strip()
            amount = float(amount_clean)

            # Skip transactions with amount <= 500
            return amount <= 500

        except (ValueError, TypeError):
            # If amount can't be parsed, don't skip
            return False

    def _is_duplicate_transaction(self, transaction: Dict[str, Any], existing_transactions: List[Dict[str, Any]]) -> bool:
        """Check if transaction is a duplicate of existing transactions"""
        try:
            for existing_txn in existing_transactions:
                # Check if sender account, receiver account, amount, and transaction ID match
                if (existing_txn.get("sender_account") == transaction.get("sender_account") and
                    existing_txn.get("receiver_account") == transaction.get("receiver_account") and
                    existing_txn.get("amount") == transaction.get("amount") and
                    existing_txn.get("receiver_transaction_id") == transaction.get("receiver_transaction_id")):
                    return True
            return False
        except Exception as e:
            self.logger.error(f"Error checking for duplicate transaction: {str(e)}")
            return False

    def organize_transactions_by_relationships(self, transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Organize transactions by their relationships.
        Places sub-transactions directly under their parent transactions based on matching criteria.

        Matching criteria:
        1. Receiver account AND transaction ID of 'Money Transfer to' transactions must match
           the sender account AND transaction ID of sub-transactions
        2. Sub-transactions are placed directly below their main transaction

        Args:
            transactions: List of transaction objects

        Returns:
            List of organized transaction objects
        """
        self.logger.info(f"Organizing {len(transactions)} transactions by relationships")

        # Create deep copies to avoid modifying the original transactions
        transactions_copy = [transaction.copy() for transaction in transactions]

        # Add a unique ID to each transaction for tracking
        for i, txn in enumerate(transactions_copy):
            txn['_temp_id'] = i

        # Separate transactions into main transactions and sub-transactions
        main_txns = []
        sub_txns = []
        other_txns = []

        for txn in transactions_copy:
            txn_type = txn.get('type', '').lower() if txn.get('type') else ''
            if 'money transfer to' in txn_type:
                main_txns.append(txn)
            elif txn_type:  # Any transaction with a type that's not 'Money Transfer to'
                sub_txns.append(txn)
            else:  # Transactions without a type
                other_txns.append(txn)

        self.logger.info(f"Found {len(main_txns)} main transactions, {len(sub_txns)} sub-transactions, and {len(other_txns)} other transactions")

        # Final organized list
        organized_transactions = []
        processed_ids = set()

        # Process each main transaction and find its matching sub-transactions
        for main_txn in main_txns:
            if main_txn['_temp_id'] in processed_ids:
                continue

            # Get receiver account and transaction ID from main transaction
            receiver_account = main_txn.get('receiver_account', '').lower()
            receiver_txn_id = main_txn.get('receiver_transaction_id', '').lower()

            # Remove any prefix from receiver account (like "A/C No.-:")
            receiver_account = self._clean_account_number(receiver_account)

            self.logger.debug(f"Processing main transaction: Receiver Account {receiver_account}, Receiver Txn ID {receiver_txn_id}")

            # Add main transaction to the result
            organized_transactions.append(main_txn)
            processed_ids.add(main_txn['_temp_id'])

            # Skip if no receiver account or transaction ID
            if not receiver_account or not receiver_txn_id:
                continue

            # Find matching sub-transactions
            matching_sub_txns = []

            # Look for exact matches (both account AND transaction ID)
            for sub_txn in sub_txns:
                if sub_txn['_temp_id'] in processed_ids:
                    continue

                sender_account = sub_txn.get('sender_account', '').lower()
                sender_txn_id = sub_txn.get('sender_transaction_id', '').lower()

                # Remove any prefix from sender account for consistent comparison
                sender_account = self._clean_account_number(sender_account)

                # Check if this is a Layer 1 transaction and card fraud type
                layer = sub_txn.get('layer')
                # Convert layer to string for comparison to handle both numeric and string values
                is_layer_1 = str(layer) == '1'
                fraud_type = sub_txn.get('fraud_type', '')
                is_card_fraud = fraud_type == 'card'

                # Fallback mechanism for Layer 1 transactions with card fraud type:
                # If sender account is not present but sender transaction ID matches receiver account
                # and receiver account is a 16-digit numerical value, use it as sender account
                if is_card_fraud and is_layer_1 and (not sender_account or sender_account.strip() == '') and sender_txn_id:
                    # Clean up receiver account to handle any prefixes
                    clean_receiver_account = self._clean_account_number(receiver_account)

                    # Check if sender transaction ID matches cleaned receiver account
                    if (sender_txn_id == clean_receiver_account and
                        clean_receiver_account.isdigit() and
                        len(clean_receiver_account) == 16):
                        self.logger.info(f"Card Fraud Fallback: Using receiver account as sender account for Layer 1 transaction. Receiver account: {clean_receiver_account}")
                        # Use cleaned receiver account as sender account
                        sub_txn['sender_account'] = clean_receiver_account
                        # Clear sender transaction ID as requested
                        sub_txn['sender_transaction_id'] = ''
                        # Update sender_account for matching
                        sender_account = clean_receiver_account
                        sender_txn_id = ''

                # Skip if no sender account or transaction ID after fallback attempt
                if not sender_account:
                    continue

                # Check for exact match (account AND transaction ID)
                account_match = sender_account == receiver_account
                txn_id_match = sender_txn_id == receiver_txn_id if sender_txn_id and receiver_txn_id else False

                if account_match and (txn_id_match or not sender_txn_id):
                    matching_sub_txns.append(sub_txn)
                    processed_ids.add(sub_txn['_temp_id'])
                    self.logger.debug(f"Match found: Account {sender_account}, Txn ID {sender_txn_id}")

            # Sort matching sub-transactions by type
            matching_sub_txns.sort(key=lambda x: x.get('type', ''))

            # Add matching sub-transactions immediately after the main transaction
            organized_transactions.extend(matching_sub_txns)

            self.logger.info(f"Added {len(matching_sub_txns)} sub-transactions for main transaction with receiver account {receiver_account}")

        # Add remaining sub-transactions
        remaining_sub_txns = [txn for txn in sub_txns if txn['_temp_id'] not in processed_ids]

        # Sort remaining sub-transactions by type
        remaining_sub_txns.sort(key=lambda x: x.get('type', ''))
        organized_transactions.extend(remaining_sub_txns)

        self.logger.info(f"Added {len(remaining_sub_txns)} remaining sub-transactions")

        # Add other transactions
        for txn in other_txns:
            if txn['_temp_id'] not in processed_ids:
                organized_transactions.append(txn)
                processed_ids.add(txn['_temp_id'])

        # Remove temporary IDs
        for txn in organized_transactions:
            if '_temp_id' in txn:
                del txn['_temp_id']

        # Sort the final organized transactions by layer number
        # First, group transactions by their relationships (main transactions with their sub-transactions)
        transaction_groups = []
        current_group = []

        for txn in organized_transactions:
            txn_type = txn.get('type', '').lower() if txn.get('type') else ''

            # If this is a main transaction and we already have a group, add the current group to our groups list
            if 'money transfer to' in txn_type and current_group:
                transaction_groups.append(current_group)
                current_group = [txn]
            else:
                # Add to the current group (either a sub-transaction or the first main transaction)
                current_group.append(txn)

        # Add the last group if it exists
        if current_group:
            transaction_groups.append(current_group)

        # Sort the groups by the layer of their main transaction (first transaction in each group)
        def get_layer_value(group):
            if not group:
                return float('inf')

            main_txn = group[0]
            layer = main_txn.get('layer')

            if layer is None:
                return float('inf')

            # Try to convert to float if it's a string
            if isinstance(layer, str):
                try:
                    return float(layer)
                except (ValueError, TypeError):
                    # If conversion fails, log warning and use infinity (will sort to the end)
                    self.logger.warning(f"Could not convert layer value '{layer}' to float, using infinity for sorting")
                    return float('inf')

            # Try to convert to float if it's another type
            try:
                return float(layer)
            except (ValueError, TypeError):
                # If conversion fails, log warning and use infinity (will sort to the end)
                self.logger.warning(f"Could not convert layer value '{layer}' to float, using infinity for sorting")
                return float('inf')

        transaction_groups.sort(key=get_layer_value)

        # Flatten the groups back into a single list
        final_organized_transactions = []
        for group in transaction_groups:
            final_organized_transactions.extend(group)

        self.logger.info(f"Organized transactions: {len(final_organized_transactions)} total (sorted by layer)")
        return final_organized_transactions
